# Swimming Capacity-Based Booking System - Schema Migration Documentation

## Overview
This document provides comprehensive documentation for implementing capacity-based booking for swimming in Grid2Play while maintaining backward compatibility with existing court-based sports.

## Pre-Migration Database State

### Current Table Schemas

#### 1. `sports` Table (TO BE MODIFIED)
```sql
-- Current schema
CREATE TABLE sports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT UNIQUE NOT NULL,
  description TEXT,
  icon TEXT,
  is_active BOOLEAN DEFAULT TRUE NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  image_url TEXT
);

-- Current constraints:
-- - PRIMARY KEY: sports_pkey (id)
-- - UNIQUE: sports_name_key (name)
-- - NOT NULL constraints on: id, name, is_active, created_at
```

#### 2. `courts` Table (TO BE MODIFIED)
```sql
-- Current schema
CREATE TABLE courts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  venue_id UUID NOT NULL REFERENCES venues(id),
  sport_id UUID NOT NULL REFERENCES sports(id),
  hourly_rate NUMERIC(10,2) DEFAULT 25.00,
  is_active BOOLEAN DEFAULT TRUE NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  court_group_id UUID REFERENCES court_groups(id),
  "Venue name/ Sports name" TEXT
);

-- Current constraints:
-- - PRIMARY KEY: courts_pkey (id)
-- - FOREIGN KEYS: courts_venue_id_fkey, courts_sport_id_fkey, courts_court_group_id_fkey
-- - NOT NULL constraints on: id, name, venue_id, sport_id, is_active, created_at, updated_at
```

#### 3. `bookings` Table (TO BE MODIFIED)
```sql
-- Current schema
CREATE TABLE bookings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  court_id UUID NOT NULL REFERENCES courts(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id),
  booking_date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  total_price NUMERIC(10,2) NOT NULL,
  guest_name TEXT,
  guest_phone TEXT,
  status booking_status DEFAULT 'confirmed' NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  payment_reference TEXT,
  payment_status TEXT DEFAULT 'pending',
  payment_method TEXT DEFAULT 'online',
  booked_by_admin_id UUID REFERENCES profiles(id),
  cancellation_reason TEXT,
  confirmation_email_sent BOOLEAN DEFAULT FALSE,
  confirmation_email_sent_at TIMESTAMPTZ,
  confirmation_email_error TEXT,
  booking_reference TEXT
);

-- booking_status ENUM: 'pending', 'confirmed', 'cancelled', 'completed'
```

#### 4. `template_slots` Table (UNCHANGED)
```sql
-- Current schema (no changes needed)
CREATE TABLE template_slots (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  court_id UUID NOT NULL REFERENCES courts(id) ON DELETE CASCADE,
  day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6),
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  is_available BOOLEAN DEFAULT TRUE NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  price TEXT NOT NULL,
  UNIQUE(court_id, day_of_week, start_time, end_time)
);
```

#### 5. `blocked_slots` Table (UNCHANGED)
```sql
-- Current schema (no changes needed)
CREATE TABLE blocked_slots (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  court_id UUID NOT NULL REFERENCES courts(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  reason TEXT,
  created_by UUID NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);
```

### Current Database Functions (TO BE MODIFIED)

#### 1. `check_booking_conflicts()` Function
- **Purpose**: Prevents overlapping bookings for court-based sports
- **Modification Required**: Skip conflict check for capacity-based sports
- **Current Logic**: Checks for time overlaps in same court/court group

#### 2. `get_available_slots()` Function  
- **Purpose**: Returns available time slots for a court on a specific date
- **Modification Required**: Return capacity information for capacity-based sports
- **Current Logic**: Returns boolean availability based on bookings and blocked slots

## Migration Plan

### Phase 1: Schema Modifications

#### 1.1 Modify `sports` Table
```sql
-- Add booking type classification
ALTER TABLE sports 
ADD COLUMN booking_type TEXT DEFAULT 'court_based' 
CHECK (booking_type IN ('court_based', 'capacity_based'));

-- Add default capacity for capacity-based sports
ALTER TABLE sports 
ADD COLUMN default_capacity INTEGER;

-- Add constraint: capacity required for capacity_based sports
ALTER TABLE sports 
ADD CONSTRAINT sports_capacity_required 
CHECK (
  (booking_type = 'court_based' AND default_capacity IS NULL) OR
  (booking_type = 'capacity_based' AND default_capacity IS NOT NULL AND default_capacity > 0)
);
```

#### 1.2 Modify `courts` Table
```sql
-- Add capacity override at court level
ALTER TABLE courts 
ADD COLUMN capacity INTEGER;

-- Add constraint: capacity override only for capacity-based sports
ALTER TABLE courts 
ADD CONSTRAINT courts_capacity_override_check 
CHECK (
  capacity IS NULL OR 
  (capacity IS NOT NULL AND capacity > 0)
);
```

#### 1.3 Modify `bookings` Table
```sql
-- Add capacity booking indicator
ALTER TABLE bookings 
ADD COLUMN is_capacity_booking BOOLEAN DEFAULT FALSE;
```

#### 1.4 Create New `capacity_bookings` Table
```sql
-- New table to track capacity per slot
CREATE TABLE capacity_bookings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  court_id UUID NOT NULL REFERENCES courts(id) ON DELETE CASCADE,
  booking_date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  current_bookings INTEGER DEFAULT 0 NOT NULL,
  max_capacity INTEGER NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  UNIQUE(court_id, booking_date, start_time, end_time),
  CHECK (current_bookings >= 0),
  CHECK (current_bookings <= max_capacity),
  CHECK (max_capacity > 0)
);

-- Add indexes for performance
CREATE INDEX idx_capacity_bookings_court_date 
ON capacity_bookings (court_id, booking_date);

CREATE INDEX idx_capacity_bookings_date_time 
ON capacity_bookings (booking_date, start_time, end_time);
```

### Phase 2: Function Modifications

#### 2.1 Create New Capacity Availability Function
```sql
-- New function for capacity-based availability
CREATE OR REPLACE FUNCTION get_available_capacity_slots(
  p_court_id UUID,
  p_date DATE
) RETURNS TABLE (
  start_time TIME,
  end_time TIME,
  available_spots INTEGER,
  max_capacity INTEGER,
  price TEXT
) LANGUAGE plpgsql AS $$
DECLARE
  court_exists BOOLEAN;
  sport_booking_type TEXT;
BEGIN
  -- Check if court exists and get sport booking type
  SELECT EXISTS(SELECT 1 FROM courts WHERE id = p_court_id), 
         s.booking_type
  INTO court_exists, sport_booking_type
  FROM courts c
  JOIN sports s ON c.sport_id = s.id
  WHERE c.id = p_court_id;
  
  IF NOT court_exists THEN
    RAISE EXCEPTION 'Court with ID % does not exist', p_court_id;
  END IF;
  
  IF sport_booking_type != 'capacity_based' THEN
    RAISE EXCEPTION 'Court % is not configured for capacity-based booking', p_court_id;
  END IF;

  RETURN QUERY
  WITH template_slots_with_capacity AS (
    SELECT 
      ts.start_time,
      ts.end_time,
      ts.price,
      COALESCE(c.capacity, s.default_capacity) as max_capacity
    FROM template_slots ts
    JOIN courts c ON ts.court_id = c.id
    JOIN sports s ON c.sport_id = s.id
    WHERE 
      ts.court_id = p_court_id
      AND ts.day_of_week = EXTRACT(DOW FROM p_date)
      AND ts.is_available = true
  ),
  current_capacity AS (
    SELECT 
      cb.start_time,
      cb.end_time,
      cb.current_bookings,
      cb.max_capacity
    FROM capacity_bookings cb
    WHERE 
      cb.court_id = p_court_id
      AND cb.booking_date = p_date
  ),
  blocked_slots AS (
    SELECT 
      bs.start_time,
      bs.end_time
    FROM blocked_slots bs
    WHERE 
      bs.court_id = p_court_id
      AND bs.date = p_date
  )
  SELECT 
    ts.start_time,
    ts.end_time,
    CASE 
      WHEN bs.start_time IS NOT NULL THEN 0  -- Blocked slot
      ELSE COALESCE(ts.max_capacity - COALESCE(cc.current_bookings, 0), ts.max_capacity)
    END as available_spots,
    ts.max_capacity,
    ts.price
  FROM template_slots_with_capacity ts
  LEFT JOIN current_capacity cc ON (
    ts.start_time = cc.start_time AND 
    ts.end_time = cc.end_time
  )
  LEFT JOIN blocked_slots bs ON (
    ts.start_time = bs.start_time AND 
    ts.end_time = bs.end_time
  )
  ORDER BY ts.start_time;
END;
$$;
```

## ROLLBACK SCRIPTS

### Complete Rollback to Original State
```sql
-- ROLLBACK SCRIPT - Execute in reverse order if migration fails

-- 1. Drop new table
DROP TABLE IF EXISTS capacity_bookings CASCADE;

-- 2. Remove new columns from bookings
ALTER TABLE bookings DROP COLUMN IF EXISTS is_capacity_booking;

-- 3. Remove new columns from courts  
ALTER TABLE courts DROP CONSTRAINT IF EXISTS courts_capacity_override_check;
ALTER TABLE courts DROP COLUMN IF EXISTS capacity;

-- 4. Remove new columns from sports
ALTER TABLE sports DROP CONSTRAINT IF EXISTS sports_capacity_required;
ALTER TABLE sports DROP COLUMN IF EXISTS default_capacity;
ALTER TABLE sports DROP COLUMN IF EXISTS booking_type;

-- 5. Drop new functions
DROP FUNCTION IF EXISTS get_available_capacity_slots(UUID, DATE);

-- 6. Restore original functions (if modified)
-- Note: Original function definitions will be preserved in backup
```

### Verification Queries
```sql
-- Verify rollback success
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'sports' AND column_name IN ('booking_type', 'default_capacity');
-- Should return 0 rows after rollback

SELECT column_name FROM information_schema.columns 
WHERE table_name = 'courts' AND column_name = 'capacity';
-- Should return 0 rows after rollback

SELECT column_name FROM information_schema.columns 
WHERE table_name = 'bookings' AND column_name = 'is_capacity_booking';
-- Should return 0 rows after rollback

SELECT table_name FROM information_schema.tables 
WHERE table_name = 'capacity_bookings';
-- Should return 0 rows after rollback
```

## Data Safety Procedures

### Pre-Migration Backup
```sql
-- Create backup tables before migration
CREATE TABLE sports_backup AS SELECT * FROM sports;
CREATE TABLE courts_backup AS SELECT * FROM courts;
CREATE TABLE bookings_backup AS SELECT * FROM bookings;

-- Backup function definitions
-- (Function definitions are preserved in this document)
```

### Post-Migration Verification
```sql
-- Verify new columns exist
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name IN ('sports', 'courts', 'bookings') 
AND column_name IN ('booking_type', 'default_capacity', 'capacity', 'is_capacity_booking');

-- Verify new table exists
SELECT table_name FROM information_schema.tables WHERE table_name = 'capacity_bookings';

-- Verify constraints
SELECT constraint_name, constraint_type 
FROM information_schema.table_constraints 
WHERE table_name IN ('sports', 'courts', 'capacity_bookings') 
AND constraint_name LIKE '%capacity%';
```

#### 2.2 Modify `check_booking_conflicts()` Function
```sql
-- Modified function to handle capacity-based sports
CREATE OR REPLACE FUNCTION public.check_booking_conflicts()
RETURNS trigger
LANGUAGE plpgsql AS $$
DECLARE
    conflicts INTEGER;
    related_courts UUID[];
    sport_booking_type TEXT;
    current_capacity INTEGER;
    max_capacity INTEGER;
BEGIN
    -- Get sport booking type
    SELECT s.booking_type INTO sport_booking_type
    FROM courts c
    JOIN sports s ON c.sport_id = s.id
    WHERE c.id = NEW.court_id;

    -- For capacity-based sports, check capacity instead of conflicts
    IF sport_booking_type = 'capacity_based' THEN
        -- Get max capacity for this court/sport
        SELECT COALESCE(c.capacity, s.default_capacity) INTO max_capacity
        FROM courts c
        JOIN sports s ON c.sport_id = s.id
        WHERE c.id = NEW.court_id;

        -- Get current bookings for this slot
        SELECT COALESCE(cb.current_bookings, 0) INTO current_capacity
        FROM capacity_bookings cb
        WHERE cb.court_id = NEW.court_id
        AND cb.booking_date = NEW.booking_date
        AND cb.start_time = NEW.start_time
        AND cb.end_time = NEW.end_time;

        -- Check if capacity would be exceeded
        IF current_capacity >= max_capacity THEN
            RAISE EXCEPTION 'Capacity limit reached for this time slot';
        END IF;

        RETURN NEW;
    END IF;

    -- Original court-based conflict logic for non-capacity sports
    SELECT ARRAY_AGG(id) INTO related_courts
    FROM public.courts
    WHERE (court_group_id = (
        SELECT court_group_id FROM public.courts WHERE id = NEW.court_id
    ) AND court_group_id IS NOT NULL)
    OR id = NEW.court_id;

    PERFORM 1
    FROM public.bookings
    WHERE booking_date = NEW.booking_date
    AND court_id = ANY(related_courts)
    AND status IN ('confirmed', 'pending')
    AND ((start_time < NEW.end_time AND end_time > NEW.start_time))
    FOR UPDATE;

    SELECT COUNT(*) INTO conflicts
    FROM public.bookings
    WHERE booking_date = NEW.booking_date
    AND court_id = ANY(related_courts)
    AND status IN ('confirmed', 'pending')
    AND ((start_time < NEW.end_time AND end_time > NEW.start_time))
    AND id != COALESCE(NEW.id, '00000000-0000-0000-0000-000000000000'::uuid);

    IF conflicts > 0 THEN
        RAISE EXCEPTION 'Booking conflicts with an existing reservation';
    END IF;

    RETURN NEW;
END;
$$;
```

#### 2.3 Create Capacity Management Triggers
```sql
-- Trigger to update capacity_bookings when booking is created/cancelled
CREATE OR REPLACE FUNCTION manage_capacity_bookings()
RETURNS trigger
LANGUAGE plpgsql AS $$
DECLARE
    sport_booking_type TEXT;
    max_capacity INTEGER;
BEGIN
    -- Get sport booking type
    SELECT s.booking_type INTO sport_booking_type
    FROM courts c
    JOIN sports s ON c.sport_id = s.id
    WHERE c.id = COALESCE(NEW.court_id, OLD.court_id);

    -- Only process capacity-based sports
    IF sport_booking_type != 'capacity_based' THEN
        IF TG_OP = 'DELETE' THEN
            RETURN OLD;
        ELSE
            RETURN NEW;
        END IF;
    END IF;

    -- Get max capacity
    SELECT COALESCE(c.capacity, s.default_capacity) INTO max_capacity
    FROM courts c
    JOIN sports s ON c.sport_id = s.id
    WHERE c.id = COALESCE(NEW.court_id, OLD.court_id);

    IF TG_OP = 'INSERT' AND NEW.status IN ('confirmed', 'pending') THEN
        -- Increase capacity count
        INSERT INTO capacity_bookings (
            court_id, booking_date, start_time, end_time,
            current_bookings, max_capacity
        ) VALUES (
            NEW.court_id, NEW.booking_date, NEW.start_time, NEW.end_time,
            1, max_capacity
        )
        ON CONFLICT (court_id, booking_date, start_time, end_time)
        DO UPDATE SET
            current_bookings = capacity_bookings.current_bookings + 1,
            updated_at = NOW();

        -- Mark booking as capacity booking
        NEW.is_capacity_booking = TRUE;

    ELSIF TG_OP = 'UPDATE' THEN
        -- Handle status changes
        IF OLD.status IN ('confirmed', 'pending') AND NEW.status NOT IN ('confirmed', 'pending') THEN
            -- Decrease capacity count (cancellation)
            UPDATE capacity_bookings
            SET current_bookings = current_bookings - 1,
                updated_at = NOW()
            WHERE court_id = NEW.court_id
            AND booking_date = NEW.booking_date
            AND start_time = NEW.start_time
            AND end_time = NEW.end_time;

        ELSIF OLD.status NOT IN ('confirmed', 'pending') AND NEW.status IN ('confirmed', 'pending') THEN
            -- Increase capacity count (reactivation)
            INSERT INTO capacity_bookings (
                court_id, booking_date, start_time, end_time,
                current_bookings, max_capacity
            ) VALUES (
                NEW.court_id, NEW.booking_date, NEW.start_time, NEW.end_time,
                1, max_capacity
            )
            ON CONFLICT (court_id, booking_date, start_time, end_time)
            DO UPDATE SET
                current_bookings = capacity_bookings.current_bookings + 1,
                updated_at = NOW();
        END IF;

    ELSIF TG_OP = 'DELETE' AND OLD.status IN ('confirmed', 'pending') THEN
        -- Decrease capacity count
        UPDATE capacity_bookings
        SET current_bookings = current_bookings - 1,
            updated_at = NOW()
        WHERE court_id = OLD.court_id
        AND booking_date = OLD.booking_date
        AND start_time = OLD.start_time
        AND end_time = OLD.end_time;
    END IF;

    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$;

-- Create trigger for capacity management
CREATE TRIGGER trigger_manage_capacity_bookings
    AFTER INSERT OR UPDATE OR DELETE ON bookings
    FOR EACH ROW
    EXECUTE FUNCTION manage_capacity_bookings();
```

### Phase 3: Data Migration and Setup

#### 3.1 Swimming Sport Setup
```sql
-- Add Swimming sport with capacity-based booking
INSERT INTO sports (name, description, booking_type, default_capacity, is_active)
VALUES ('Swimming', 'Swimming pool access per person', 'capacity_based', 10, true)
ON CONFLICT (name) DO UPDATE SET
    booking_type = 'capacity_based',
    default_capacity = 10,
    description = 'Swimming pool access per person';
```

#### 3.2 Update Existing Sports
```sql
-- Ensure existing sports are marked as court_based
UPDATE sports
SET booking_type = 'court_based'
WHERE booking_type IS NULL;
```

## COMPLETE ROLLBACK SCRIPTS

### Function Rollback
```sql
-- Restore original check_booking_conflicts function
CREATE OR REPLACE FUNCTION public.check_booking_conflicts()
RETURNS trigger
LANGUAGE plpgsql AS $$
DECLARE
    conflicts INTEGER;
    related_courts UUID[];
BEGIN
    SELECT ARRAY_AGG(id) INTO related_courts
    FROM public.courts
    WHERE (court_group_id = (
        SELECT court_group_id FROM public.courts WHERE id = NEW.court_id
    ) AND court_group_id IS NOT NULL)
    OR id = NEW.court_id;

    PERFORM 1
    FROM public.bookings
    WHERE booking_date = NEW.booking_date
    AND court_id = ANY(related_courts)
    AND status IN ('confirmed', 'pending')
    AND ((start_time < NEW.end_time AND end_time > NEW.start_time))
    FOR UPDATE;

    SELECT COUNT(*) INTO conflicts
    FROM public.bookings
    WHERE booking_date = NEW.booking_date
    AND court_id = ANY(related_courts)
    AND status IN ('confirmed', 'pending')
    AND ((start_time < NEW.end_time AND end_time > NEW.start_time))
    AND id != COALESCE(NEW.id, '00000000-0000-0000-0000-000000000000'::uuid);

    IF conflicts > 0 THEN
        RAISE EXCEPTION 'Booking conflicts with an existing reservation';
    END IF;

    RETURN NEW;
END;
$$;

-- Drop new triggers and functions
DROP TRIGGER IF EXISTS trigger_manage_capacity_bookings ON bookings;
DROP FUNCTION IF EXISTS manage_capacity_bookings();
DROP FUNCTION IF EXISTS get_available_capacity_slots(UUID, DATE);
```

## Step-by-Step Migration Execution

### Step 1: Pre-Migration Verification
```sql
-- Verify current state
SELECT COUNT(*) as total_sports FROM sports;
SELECT COUNT(*) as total_courts FROM courts;
SELECT COUNT(*) as total_bookings FROM bookings;
SELECT COUNT(*) as active_bookings FROM bookings WHERE status IN ('confirmed', 'pending');

-- Check for any existing capacity-related columns (should be 0)
SELECT COUNT(*) FROM information_schema.columns
WHERE table_name IN ('sports', 'courts', 'bookings')
AND column_name IN ('booking_type', 'default_capacity', 'capacity', 'is_capacity_booking');
```

### Step 2: Create Backups
```sql
-- Create backup tables
CREATE TABLE sports_backup_$(date +%Y%m%d) AS SELECT * FROM sports;
CREATE TABLE courts_backup_$(date +%Y%m%d) AS SELECT * FROM courts;
CREATE TABLE bookings_backup_$(date +%Y%m%d) AS SELECT * FROM bookings;
```

### Step 3: Execute Schema Changes (Phase 1)
```sql
-- Execute each ALTER TABLE statement individually with verification
-- (See Phase 1 section above)
```

### Step 4: Execute Function Changes (Phase 2)
```sql
-- Execute each function creation/modification individually
-- (See Phase 2 section above)
```

### Step 5: Post-Migration Verification
```sql
-- Verify all changes applied successfully
SELECT column_name, data_type, column_default
FROM information_schema.columns
WHERE table_name = 'sports' AND column_name IN ('booking_type', 'default_capacity');

SELECT column_name, data_type
FROM information_schema.columns
WHERE table_name = 'courts' AND column_name = 'capacity';

SELECT column_name, data_type, column_default
FROM information_schema.columns
WHERE table_name = 'bookings' AND column_name = 'is_capacity_booking';

SELECT table_name FROM information_schema.tables WHERE table_name = 'capacity_bookings';

-- Test capacity function
SELECT * FROM get_available_capacity_slots(
    (SELECT id FROM courts LIMIT 1),
    CURRENT_DATE
) LIMIT 1;
```

## Next Steps
1. Execute Phase 1 schema modifications with verification
2. Execute Phase 2 function modifications
3. Execute Phase 3 data migration
4. Implement backend API changes
5. Update frontend UI components
6. Test capacity booking flow end-to-end

## Breaking Changes
- None - All changes are additive and backward compatible
- Existing court-based sports continue to work unchanged
- New capacity logic only applies to sports with booking_type = 'capacity_based'

## Dependencies
- Requires existing tables: sports, courts, bookings, template_slots, blocked_slots
- Requires existing functions: All current functions remain functional
- No external API changes required for database layer
